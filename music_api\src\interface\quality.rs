use std::ops::Deref;

use sea_orm::FromJsonQueryResult;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>ult, Debug, <PERSON><PERSON>, Eq, PartialEq, Serialize, Deserialize, FromJsonQueryResult)]
pub struct Quality {
    pub summary: String,
    pub bitrate: Option<String>,
    pub format: Option<String>,
    pub size: Option<String>,
}

#[derive(De<PERSON>ult, Debug, <PERSON>lone, Eq, PartialEq, Serialize, Deserialize, FromJsonQueryResult)]
pub struct QualityVec(pub Vec<Quality>);

impl From<Vec<Quality>> for QualityVec {
    fn from(qualities: Vec<Quality>) -> Self {
        Self(qualities)
    }
}

impl Deref for QualityVec {
    type Target = Vec<Quality>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
