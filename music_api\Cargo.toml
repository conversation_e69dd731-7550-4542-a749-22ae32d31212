[package]
name = "music_api"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
aes = "0.8.4"
anyhow = "1.0.96"
async-trait = "0.1.86"
base64 = "0.22.1"
cbc = "0.1.2"
crypto = "0.5.1"
ecb = "0.1.2"
env_logger = "0.11.6"
futures = "0.3.31"
generic-array = "1.2.0"
hex = "0.4.3"
lazy_static = "1.5.0"
log = "0.4.26"
md-5 = "0.10.6"
once_cell = "1.20.3"
rand = "0.9.0"
regex = "1.11.1"
reqwest = { version = "0.12.12", features = [
    "json",
    "rustls-tls",
    "rustls-tls-webpki-roots",
], default-features = false }
reqwest-middleware = { git = "https://github.com/TrueLayer/reqwest-middleware" }
reqwest-retry = { git = "https://github.com/TrueLayer/reqwest-middleware" }
rsa = { version = "0.9.7", features = ["hazmat"] }
sea-orm = { version = "1.1.5", default-features = false, features = [
    "sqlx-all",
    "runtime-tokio-rustls",
    "serde_json",
    "with-json",
] }
sea-orm-migration = { version = "1.1.5", features = [
    "sqlx-mysql",
    "sqlx-postgres",
    "sqlx-sqlite",
    "runtime-tokio-rustls",
], default-features = false }

serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0.139"
sha2 = "0.10.8"
# sqlx = { version = "0.8.2", features = ["runtime-tokio", "sqlite"] }
tokio = { version = "1.43.0", features = ["full"] }
urlencoding = "2.1.3"

[dev-dependencies]
serial_test = "3.2.0"
