import 'package:flutter/cupertino.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/utils/colors.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// JS音乐源配置
class JsSourceConfig {
  final String name;
  final String version;
  final String author;
  final String description;
  final List<String> supportedQualities;

  const JsSourceConfig({
    required this.name,
    required this.version,
    required this.author,
    required this.description,
    required this.supportedQualities,
  });

  factory JsSourceConfig.fromJson(Map<String, dynamic> json) {
    return JsSourceConfig(
      name: json['name'] ?? '未知音乐源',
      version: json['version'] ?? '1.0.0',
      author: json['author'] ?? '未知作者',
      description: json['description'] ?? '无描述',
      supportedQualities: List<String>.from(json['supportedQualities'] ?? ['128k', '320k']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'version': version,
      'author': author,
      'description': description,
      'supportedQualities': supportedQualities,
    };
  }
}

/// JS音乐源状态
enum JsSourceStatus {
  loading,
  loaded,
  error,
}

/// JS音乐源实体
class JsMusicSource {
  String id;
  final String url;
  String localPath;
  JsSourceConfig? config;
  JsSourceStatus status;
  String? errorMessage;
  DateTime lastUpdate;

  JsMusicSource({
    required this.url,
    required this.localPath,
    String? id,
    this.config,
    this.status = JsSourceStatus.loading,
    this.errorMessage,
    DateTime? lastUpdate,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       lastUpdate = lastUpdate ?? DateTime.now();

  String get displayName => config?.name ?? '未知音乐源';
  String get displayVersion => config?.version ?? '1.0.0';
  String get displayAuthor => config?.author ?? '未知作者';
  String get displayDescription => config?.description ?? '无描述';

  void updateStatus(JsSourceStatus newStatus, {String? error}) {
    status = newStatus;
    errorMessage = error;
    lastUpdate = DateTime.now();
  }

  String get statusText {
    switch (status) {
      case JsSourceStatus.loading:
        return '加载中...';
      case JsSourceStatus.loaded:
        return '已加载';
      case JsSourceStatus.error:
        return '加载失败';
    }
  }

  Color get statusColor {
    switch (status) {
      case JsSourceStatus.loading:
        return CupertinoColors.systemBlue;
      case JsSourceStatus.loaded:
        return CupertinoColors.systemGreen;
      case JsSourceStatus.error:
        return CupertinoColors.systemRed;
    }
  }
}

/// JS音乐源管理页面
class JsMusicSourceManagementPage extends StatefulWidget {
  const JsMusicSourceManagementPage({super.key});

  @override
  State<JsMusicSourceManagementPage> createState() => _JsMusicSourceManagementPageState();
}

class _JsMusicSourceManagementPageState extends State<JsMusicSourceManagementPage> {
  List<JsMusicSource> _sources = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    globalTalker.info('[JsMusicSource] Initializing JS Music Source Management Page');
    _loadSources();
  }

  Future<void> _loadSources() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final sourcesJson = prefs.getStringList('js_music_sources') ?? [];

      _sources = sourcesJson.map((json) {
        try {
          final data = jsonDecode(json);
          return JsMusicSource(
            id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            url: data['url'] ?? '',
            localPath: data['localPath'] ?? '',
            config: data['config'] != null ? JsSourceConfig.fromJson(data['config']) : null,
            status: JsSourceStatus.values[data['status'] ?? 0],
            errorMessage: data['errorMessage'],
            lastUpdate: DateTime.parse(data['lastUpdate'] ?? DateTime.now().toIso8601String()),
          );
        } catch (e) {
          globalTalker.error('[JsMusicSource] Failed to parse source: $e');
          return null;
        }
      }).where((source) => source != null).cast<JsMusicSource>().toList();
    } catch (e) {
      globalTalker.error('[JsMusicSource] Failed to load sources: $e');
      _sources = [];
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _saveSources() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sourcesJson = _sources.map((source) {
        return jsonEncode({
          'id': source.id,
          'url': source.url,
          'localPath': source.localPath,
          'config': source.config?.toJson(),
          'status': source.status.index,
          'errorMessage': source.errorMessage,
          'lastUpdate': source.lastUpdate.toIso8601String(),
        });
      }).toList();

      final success = await prefs.setStringList('js_music_sources', sourcesJson);
      if (success) {
        globalTalker.info('[JsMusicSource] Successfully saved ${_sources.length} sources');
      } else {
        globalTalker.error('[JsMusicSource] Failed to save sources to SharedPreferences');
      }
    } catch (e) {
      globalTalker.error('[JsMusicSource] Failed to save sources: $e');
    }
  }

  Future<void> _addSource() async {
    final url = await _showAddSourceDialog();
    if (url != null && url.isNotEmpty) {
      // 检查是否已存在相同URL的源
      if (_sources.any((source) => source.url == url)) {
        LogToast.error('添加失败', '该音乐源已存在', '[JsMusicSource] Source already exists');
        return;
      }

      final newSource = JsMusicSource(
        url: url,
        localPath: '',
        status: JsSourceStatus.loading,
      );

      setState(() {
        _sources.add(newSource);
      });

      await _loadSourceFromUrl(newSource);
    }
  }

  Future<void> _loadSourceFromUrl(JsMusicSource source) async {
    try {
      globalTalker.info('[JsMusicSource] Loading source from: ${source.url}');

      // 下载JS源代码
      final response = await http.get(
        Uri.parse(source.url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }

      final jsCode = response.body;

      // 解析CONFIG信息
      JsSourceConfig? config;
      try {
        config = _parseConfigFromJs(jsCode);
      } catch (e) {
        globalTalker.warning('[JsMusicSource] Failed to parse CONFIG: $e');
        config = JsSourceConfig(
          name: '未知音乐源',
          version: '1.0.0',
          author: '未知作者',
          description: '从 ${source.url} 加载的音乐源',
          supportedQualities: ['128k', '320k'],
        );
      }

      setState(() {
        source.config = config;
        source.updateStatus(JsSourceStatus.loaded);
      });

      await _saveSources();
      LogToast.success('添加成功', '音乐源"${config.name}"已成功添加', '[JsMusicSource] Source added successfully');

    } catch (e) {
      setState(() {
        source.updateStatus(JsSourceStatus.error, error: e.toString());
      });
      await _saveSources();
      LogToast.error('添加失败', '加载音乐源失败: $e', '[JsMusicSource] Failed to load source: $e');
    }
  }

  JsSourceConfig _parseConfigFromJs(String jsCode) {
    // 简单的正则表达式解析CONFIG对象
    final configRegex = RegExp(r'const\s+CONFIG\s*=\s*\{([^}]+)\}', multiLine: true, dotAll: true);
    final match = configRegex.firstMatch(jsCode);

    if (match == null) {
      throw Exception('CONFIG object not found in JS code');
    }

    final configContent = match.group(1)!;

    // 解析各个字段
    String name = _extractStringValue(configContent, 'name') ?? '未知音乐源';
    String version = _extractStringValue(configContent, 'version') ?? '1.0.0';
    String author = _extractStringValue(configContent, 'author') ?? '未知作者';
    String description = _extractStringValue(configContent, 'description') ?? '无描述';

    // 解析supportedQualities数组
    List<String> supportedQualities = ['128k', '320k'];
    final qualitiesRegex = RegExp(r'supportedQualities\s*:\s*\[([^\]]+)\]');
    final qualitiesMatch = qualitiesRegex.firstMatch(configContent);
    if (qualitiesMatch != null) {
      final qualitiesStr = qualitiesMatch.group(1)!;
      supportedQualities = qualitiesStr
          .split(',')
          .map((q) => q.trim().replaceAll('"', '').replaceAll("'", ''))
          .where((q) => q.isNotEmpty)
          .toList();
    }

    return JsSourceConfig(
      name: name,
      version: version,
      author: author,
      description: description,
      supportedQualities: supportedQualities,
    );
  }

  String? _extractStringValue(String content, String key) {
    final regex = RegExp('$key\\s*:\\s*["\']([^"\']+)["\']');
    final match = regex.firstMatch(content);
    return match?.group(1);
  }

  Future<String?> _showAddSourceDialog() async {
    final controller = TextEditingController();
    
    return showCupertinoDialog<String>(
      context: context,
      builder: (context) {
        final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
        
        return CupertinoAlertDialog(
          title: Text(
            '添加音乐源',
            style: const TextStyle().useSystemChineseFont(),
          ),
          content: Column(
            children: [
              const SizedBox(height: 16),
              CupertinoTextField(
                controller: controller,
                placeholder: '请输入JS音乐源URL',
                style: TextStyle(
                  color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                ).useSystemChineseFont(),
              ),
            ],
          ),
          actions: [
            CupertinoDialogAction(
              child: Text(
                '取消',
                style: const TextStyle().useSystemChineseFont(),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            CupertinoDialogAction(
              child: Text(
                '添加',
                style: const TextStyle().useSystemChineseFont(),
              ),
              onPressed: () => Navigator.of(context).pop(controller.text),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteSource(JsMusicSource source) async {
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(
          '删除音乐源',
          style: const TextStyle().useSystemChineseFont(),
        ),
        content: Text(
          '确定要删除音乐源"${source.displayName}"吗？',
          style: const TextStyle().useSystemChineseFont(),
        ),
        actions: [
          CupertinoDialogAction(
            child: Text(
              '取消',
              style: const TextStyle().useSystemChineseFont(),
            ),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: Text(
              '删除',
              style: const TextStyle().useSystemChineseFont(),
            ),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _sources.remove(source);
      });
      await _saveSources();
      LogToast.success('删除成功', '音乐源"${source.displayName}"已删除', '[JsMusicSource] Source deleted');
    }
  }

  Future<void> _reloadSource(JsMusicSource source) async {
    setState(() {
      source.updateStatus(JsSourceStatus.loading);
    });

    await _loadSourceFromUrl(source);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;

    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(
          'JS音乐源管理',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _isLoading ? null : _addSource,
          child: Icon(
            CupertinoIcons.add,
            color: _isLoading ? CupertinoColors.systemGrey : activeIconRed,
          ),
        ),
      ),
      child: SafeArea(
        child: _isLoading && _sources.isEmpty
            ? const Center(
                child: CupertinoActivityIndicator(),
              )
            : _sources.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.music_note_2,
                          size: 64,
                          color: CupertinoColors.systemGrey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '暂无音乐源',
                          style: TextStyle(
                            color: CupertinoColors.systemGrey,
                            fontSize: 18,
                          ).useSystemChineseFont(),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '点击右上角"+"添加音乐源',
                          style: TextStyle(
                            color: CupertinoColors.systemGrey2,
                            fontSize: 14,
                          ).useSystemChineseFont(),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _sources.length,
                    itemBuilder: (context, index) {
                      final source = _sources[index];
                      return _buildSourceItem(source, isDarkMode, textColor);
                    },
                  ),
      ),
    );
  }

  Widget _buildSourceItem(JsMusicSource source, bool isDarkMode, Color textColor) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode ? CupertinoColors.darkBackgroundGray : CupertinoColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: CupertinoColors.systemGrey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        source.displayName,
                        style: TextStyle(
                          color: textColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ).useSystemChineseFont(),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '版本: ${source.displayVersion} | 作者: ${source.displayAuthor}',
                        style: TextStyle(
                          color: CupertinoColors.systemGrey,
                          fontSize: 14,
                        ).useSystemChineseFont(),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: source.statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    source.statusText,
                    style: TextStyle(
                      color: source.statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ).useSystemChineseFont(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              source.displayDescription,
              style: TextStyle(
                color: CupertinoColors.systemGrey,
                fontSize: 14,
              ).useSystemChineseFont(),
            ),
            if (source.errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                '错误: ${source.errorMessage}',
                style: TextStyle(
                  color: CupertinoColors.systemRed,
                  fontSize: 12,
                ).useSystemChineseFont(),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CupertinoButton(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  minSize: 0,
                  onPressed: source.status == JsSourceStatus.loading ? null : () => _reloadSource(source),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        CupertinoIcons.refresh,
                        size: 16,
                        color: source.status == JsSourceStatus.loading 
                            ? CupertinoColors.systemGrey 
                            : activeIconRed,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '重载',
                        style: TextStyle(
                          color: source.status == JsSourceStatus.loading 
                              ? CupertinoColors.systemGrey 
                              : activeIconRed,
                          fontSize: 14,
                        ).useSystemChineseFont(),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                CupertinoButton(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  minSize: 0,
                  onPressed: () => _deleteSource(source),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        CupertinoIcons.delete,
                        size: 16,
                        color: CupertinoColors.systemRed,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '删除',
                        style: TextStyle(
                          color: CupertinoColors.systemRed,
                          fontSize: 14,
                        ).useSystemChineseFont(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
