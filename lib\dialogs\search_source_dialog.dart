import 'package:flutter/cupertino.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/services/search_source_manager.dart';
import 'package:app_rhyme/utils/colors.dart';

/// 显示搜索源选择对话框
Future<SearchSource?> showSearchSourceDialog(BuildContext context) async {
  final manager = SearchSourceManager();
  await manager.refreshSources();
  
  final availableSources = manager.availableSources;
  final currentSource = manager.selectedSource;
  
  if (availableSources.isEmpty) {
    // 如果没有可用源，显示提示
    return showCupertinoDialog<SearchSource?>(
      context: context,
      builder: (BuildContext context) {
        final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
        final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
        
        return CupertinoAlertDialog(
          title: Text(
            '无可用搜索源',
            style: TextStyle(color: textColor).useSystemChineseFont(),
          ),
          content: Text(
            '请先添加JS音乐源或检查网络连接',
            style: TextStyle(color: textColor).useSystemChineseFont(),
          ),
          actions: [
            CupertinoDialogAction(
              child: Text(
                '确定',
                style: const TextStyle().useSystemChineseFont(),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  return showCupertinoModalPopup<SearchSource?>(
    context: context,
    builder: (BuildContext context) {
      final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
      final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
      
      return CupertinoActionSheet(
        title: Text(
          '选择搜索源',
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ).useSystemChineseFont(),
        ),
        message: Text(
          '当前: ${currentSource?.name ?? '未选择'}',
          style: TextStyle(
            color: CupertinoColors.systemGrey,
            fontSize: 14,
          ).useSystemChineseFont(),
        ),
        actions: availableSources.map((source) {
          final isSelected = source == currentSource;
          
          return CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop(source),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        source.name,
                        style: TextStyle(
                          color: isSelected ? activeIconRed : textColor,
                          fontSize: 16,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ).useSystemChineseFont(),
                      ),
                      if (source.description != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          source.description!,
                          style: TextStyle(
                            color: CupertinoColors.systemGrey,
                            fontSize: 12,
                          ).useSystemChineseFont(),
                        ),
                      ],
                    ],
                  ),
                ),
                if (isSelected) ...[
                  const SizedBox(width: 8),
                  Icon(
                    CupertinoIcons.checkmark_circle_fill,
                    color: activeIconRed,
                    size: 20,
                  ),
                ],
              ],
            ),
          );
        }).toList(),
        cancelButton: CupertinoActionSheetAction(
          child: Text(
            '取消',
            style: const TextStyle().useSystemChineseFont(),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      );
    },
  );
}

/// 搜索源类型图标
Widget getSearchSourceTypeIcon(SearchSourceType type) {
  switch (type) {
    case SearchSourceType.musicApi:
      return const Icon(
        CupertinoIcons.music_note_2,
        size: 16,
        color: CupertinoColors.systemBlue,
      );
    case SearchSourceType.jsSource:
      return const Icon(
        CupertinoIcons.textformat,
        size: 16,
        color: CupertinoColors.systemGreen,
      );
  }
}

/// 搜索源类型名称
String getSearchSourceTypeName(SearchSourceType type) {
  switch (type) {
    case SearchSourceType.musicApi:
      return '内置源';
    case SearchSourceType.jsSource:
      return 'JS源';
  }
}
