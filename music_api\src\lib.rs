pub mod data;
pub mod interface;
pub mod server;
pub mod music_list;

use std::sync::{Arc, LazyLock};

use reqwest_middleware::{ClientBuilder, ClientWithMiddleware};
use reqwest_retry::{policies::ExponentialBackoff, RetryTransientMiddleware};
use sea_orm::DatabaseConnection;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};



// 重新导出MusicAggregator结构体
pub use interface::music_aggregator::MusicAggregator;
// 重新导出Music结构体
pub use interface::music_aggregator::Music;
// 重新导出Quality结构体
pub use interface::quality::Quality;
// 重新导出music_list模块
pub use music_list::ExtraInfo;
// 重新导出Playlist作为MusicList
pub use interface::playlist::Playlist as MusicList;

/// MusicInfo结构体，用于JS音乐源搜索结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MusicInfo {
    /// 与歌曲/平台本身无关的id，代表的仅仅是其在当前 自定义歌单 中的id
    pub id: i64,
    /// 歌曲的来源平台
    pub source: String,
    /// 歌曲的名字
    pub name: String,
    /// 歌曲的演唱者的集合
    pub artist: Vec<String>,
    /// 歌曲的时长(s)
    pub duration: Option<u32>,
    /// 歌曲的专辑的名称
    pub album: Option<String>,
    /// 歌曲的可选音质
    pub qualities: Vec<Quality>,
    /// 歌曲默认选取的音质，可以作为本地持久储存，来为实现每首歌的默认音质均可自定义的功能
    pub default_quality: Option<Quality>,
    /// 歌曲的艺术照
    pub art_pic: Option<String>,
    /// 歌曲的歌词
    pub lyric: Option<String>,
}

/// MusicFuzzFilter结构体，用于音乐模糊搜索过滤
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MusicFuzzFilter {
    pub name: Option<String>,
    pub artist: Vec<String>,
    pub album: Option<String>,
}

/// MusicListInfo结构体，用于音乐列表信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MusicListInfo {
    pub id: i64,
    pub name: String,
    pub art_pic: String,
    pub desc: String,
    pub extra: Option<music_list::ExtraInfo>,
}

pub static CLIENT: LazyLock<ClientWithMiddleware> = LazyLock::new(|| {
    ClientBuilder::new(
        reqwest::Client::builder()
            .danger_accept_invalid_certs(true)
            .build()
            .unwrap(),
    )
    .with(RetryTransientMiddleware::new_with_policy(
        ExponentialBackoff::builder().build_with_max_retries(2),
    ))
    .build()
});

static DB_POOL: LazyLock<Arc<RwLock<Option<DatabaseConnection>>>> =
    LazyLock::new(|| Arc::new(RwLock::new(None)));
