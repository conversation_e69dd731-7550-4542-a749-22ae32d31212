use serde::{Deserialize, Serialize};

use super::server::MusicServer;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>q, <PERSON>ialEq, Serialize, Deserialize)]
pub struct MusicChart {
    pub name: String,
    pub summary: Option<String>,
    pub cover: Option<String>,
    pub id: String,
}

#[derive(Debug, <PERSON><PERSON>, Eq, PartialEq, Serialize, Deserialize)]
pub struct MusicChartCollection {
    pub name: String,
    pub summary: Option<String>,
    pub charts: Vec<MusicChart>,
}

#[derive(Debug, <PERSON><PERSON>, Eq, PartialEq, Serialize, Deserialize)]
pub struct ServerMusicChartCollection {
    pub server: MusicServer,
    pub collections: Vec<MusicChartCollection>,
}
