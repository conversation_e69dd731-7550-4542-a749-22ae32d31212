use std::ops::Deref;

use sea_orm::FromJsonQueryResult;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bug, <PERSON><PERSON>, <PERSON>q, <PERSON>ialEq, Serialize, Deserialize, FromJsonQueryResult)]
pub struct Artist {
    pub name: String,
    pub id: Option<String>,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>, Eq, PartialEq, Serialize, Deserialize, FromJsonQueryResult)]
pub struct ArtistVec(pub Vec<Artist>);

impl From<Vec<Artist>> for ArtistVec {
    fn from(qualities: Vec<Artist>) -> Self {
        Self(qualities)
    }
}

impl Deref for ArtistVec {
    type Target = Vec<Artist>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
