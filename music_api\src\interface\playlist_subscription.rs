use anyhow::Result;
use sea_orm::FromJsonQueryResult;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize, FromJsonQueryResult)]
pub struct PlayListSubscription {
    #[serde(rename = "n")]
    pub name: String,
    #[serde(rename = "s")]
    pub share: String,
}

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize, FromJsonQueryResult)]
pub struct PlayListSubscriptionVec(pub Vec<PlayListSubscription>);
