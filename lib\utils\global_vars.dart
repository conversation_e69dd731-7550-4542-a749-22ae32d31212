import 'package:app_rhyme/audioControl/audio_controller.dart';
import 'package:app_rhyme/src/rust/api/init.dart';
import 'package:app_rhyme/src/rust/api/types/config.dart';
import 'package:app_rhyme/types/chore.dart';
import 'package:get/get.dart';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:talker/talker.dart';

Talker globalTalker = Talker();

// 在`init_backend.dart` 中被初始化
late Config globalConfig;

/// 临时音质管理器
/// 用于管理用户手动切换的音质，不影响设置页的默认音质
class TemporaryQualityManager extends GetxController {
  // 当前歌曲的临时音质设置 (歌曲ID -> 音质名称)
  final RxMap<String, String> _temporaryQualities = <String, String>{}.obs;

  /// 设置当前歌曲的临时音质
  void setTemporaryQuality(String musicId, String quality) {
    _temporaryQualities[musicId] = quality;
    globalTalker.info('[TemporaryQualityManager] 设置临时音质: $musicId -> $quality');
  }

  /// 获取当前歌曲的临时音质
  String? getTemporaryQuality(String musicId) {
    final quality = _temporaryQualities[musicId];
    if (quality != null) {
      globalTalker.info('[TemporaryQualityManager] 获取临时音质: $musicId -> $quality');
    }
    return quality;
  }

  /// 清除当前歌曲的临时音质设置
  void clearTemporaryQuality(String musicId) {
    if (_temporaryQualities.containsKey(musicId)) {
      final quality = _temporaryQualities.remove(musicId);
      globalTalker.info('[TemporaryQualityManager] 清除临时音质: $musicId -> $quality');
    }
  }

  /// 清除所有临时音质设置
  void clearAllTemporaryQualities() {
    _temporaryQualities.clear();
  }

  /// 检查是否有临时音质设置
  bool hasTemporaryQuality(String musicId) {
    return _temporaryQualities.containsKey(musicId);
  }
}

// 全局临时音质管理器
late TemporaryQualityManager globalTemporaryQualityManager;

// ExternApiEvaler已删除，现在使用JS音乐源
late AudioHandler globalAudioHandler;
late AudioUiController globalAudioUiController;
late PackageInfo globalPackageInfo;
late String globalDocumentPath;
late Connectivity globalConnectivity;
ConnectivityStateSimple globalConnectivityStateSimple =
    ConnectivityStateSimple.none;

// 初始化全局变量
// 即可用于在App启动时也可用于配置更新时
Future<void> initGlobalVars() async {
  // 初始化rust全局配置，将documentPath设置为应用程序文档目录
  globalDocumentPath = (await getApplicationDocumentsDirectory()).path;
  // 初始化全局变量globalConfig
  globalConfig = await initBackend(storeRoot: globalDocumentPath);
  // ExternApiEvaler已删除，现在使用JS音乐源
  // 初始化临时音质管理器
  globalTemporaryQualityManager = TemporaryQualityManager();
  // 初始化应用包信息
  globalPackageInfo = await PackageInfo.fromPlatform();
  // 监听网络状态变化
  globalConnectivity = Connectivity();
  globalConnectivity.onConnectivityChanged
      .listen((List<ConnectivityResult> connectivityResult) {
    if (connectivityResult.contains(ConnectivityResult.wifi)) {
      globalConnectivityStateSimple = ConnectivityStateSimple.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
      globalConnectivityStateSimple = ConnectivityStateSimple.mobile;
    } else {
      globalConnectivityStateSimple = ConnectivityStateSimple.none;
    }
  });
}
