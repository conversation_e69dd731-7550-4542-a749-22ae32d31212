import 'dart:async';
import 'dart:convert';
import 'package:app_rhyme/types/lyric_ui.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/types/music_container.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/time_parser.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lyric/lyrics_reader.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

class LyricDisplay extends StatefulWidget {
  final double maxHeight;
  final bool isDarkMode;
  const LyricDisplay(
      {super.key, required this.maxHeight, required this.isDarkMode});

  @override
  LyricDisplayState createState() => LyricDisplayState();
}

class LyricDisplayState extends State<LyricDisplay> {
  late LyricUI lyricUI;
  var lyricModel =
      LyricsModelBuilder.create().bindLyricToMain("[00:00.00]无歌词").getModel();
  late StreamSubscription<MusicContainer?> stream;
  bool _showTranslation = false;

  @override
  void initState() {
    super.initState();
    lyricUI = AppleMusicLyricUi();
    _updateLyricModel();
    stream = globalAudioHandler.playingMusic.listen((p0) {
      setState(() {
        _updateLyricModel();
      });
    });
  }

  void _updateLyricModel() async {
    final currentMusic = globalAudioHandler.playingMusic.value;
    String mainLyric = currentMusic?.info.lyric ?? "[00:00.00]无歌词";
    String? translationLyric;

    // 如果没有歌词或歌词为空，尝试从JS音乐源获取
    if (mainLyric.isEmpty || mainLyric == "[00:00.00]无歌词" || mainLyric == "null") {
      await _fetchLyricFromJsSource();
      // 重新获取更新后的歌词
      final updatedMusic = globalAudioHandler.playingMusic.value;
      mainLyric = updatedMusic?.info.lyric ?? "[00:00.00]无歌词";
    }

    // 尝试从JS音乐源获取翻译歌词
    if (_showTranslation) {
      translationLyric = await _fetchTranslationLyricFromJsSource();
      globalTalker.info('[LyricDisplay] 翻译歌词状态: ${translationLyric != null ? "有翻译(${translationLyric!.length}字符)" : "无翻译"}');
    }

    if (_showTranslation && translationLyric != null && translationLyric.isNotEmpty) {
      globalTalker.info('[LyricDisplay] 构建带翻译的歌词模型');
      lyricModel = LyricsModelBuilder.create()
          .bindLyricToMain(mainLyric)
          .bindLyricToExt(translationLyric)
          .getModel();
    } else {
      globalTalker.info('[LyricDisplay] 构建普通歌词模型');
      lyricModel = LyricsModelBuilder.create()
          .bindLyricToMain(mainLyric)
          .getModel();
    }

    // 强制刷新UI
    setState(() {});
  }

  /// 从JS音乐源获取歌词
  Future<void> _fetchLyricFromJsSource() async {
    final currentMusic = globalAudioHandler.playingMusic.value;
    if (currentMusic == null) return;

    try {
      // 强制刷新歌词
      await currentMusic.aggregator.fetchLyric();
      // 等待一小段时间确保歌词更新
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      globalTalker.error('[LyricDisplay] 获取歌词失败: $e');
    }
  }

  /// 从JS音乐源获取翻译歌词
  Future<String?> _fetchTranslationLyricFromJsSource() async {
    final currentMusic = globalAudioHandler.playingMusic.value;
    if (currentMusic == null) return null;

    try {
      // 从extra字段获取网易云音乐ID
      String? musicId = _extractNetEaseIdFromExtra(currentMusic.extra ?? "");

      // 如果extra中没有ID，尝试使用MusicInfo的ID
      if (musicId == null || musicId.isEmpty) {
        String id = currentMusic.info.id.toString();
        if (id != '0' && id.isNotEmpty && id != 'null') {
          musicId = id;
        }
      }

      if (musicId != null && musicId.isNotEmpty) {
        return await _getNetEaseTranslationLyric(musicId);
      }

      return null;
    } catch (e) {
      globalTalker.error('[LyricDisplay] 获取翻译歌词失败: $e');
      return null;
    }
  }

  /// 从extra字段提取网易云音乐ID
  String? _extractNetEaseIdFromExtra(String extra) {
    try {
      // 1. 直接是数字ID
      if (RegExp(r'^\d+$').hasMatch(extra)) {
        return extra;
      }

      // 2. 包含id=的格式
      final idMatch = RegExp(r'id=(\d+)').firstMatch(extra);
      if (idMatch != null) {
        return idMatch.group(1);
      }

      // 3. JSON格式
      try {
        final json = jsonDecode(extra);
        if (json is Map) {
          for (final key in ['id', 'music_id', 'netease_id', 'song_id']) {
            if (json.containsKey(key)) {
              return json[key].toString();
            }
          }
        }
      } catch (e) {
        // JSON解析失败，继续其他方法
      }

      // 4. 从任何地方提取数字ID
      final numberMatch = RegExp(r'\b(\d{6,})\b').firstMatch(extra);
      if (numberMatch != null) {
        return numberMatch.group(1);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取网易云音乐翻译歌词
  Future<String?> _getNetEaseTranslationLyric(String musicId) async {
    try {
      globalTalker.info('[LyricDisplay] 获取翻译歌词: $musicId');

      // 使用网易云音乐歌词API
      final lyricUrl = 'https://music.163.com/api/song/lyric?id=$musicId&lv=1&kv=1&tv=-1';

      final response = await http.get(
        Uri.parse(lyricUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://music.163.com/',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['tlyric'] != null && data['tlyric']['lyric'] != null) {
          String tlyric = data['tlyric']['lyric'];
          if (tlyric.isNotEmpty) {
            globalTalker.info('[LyricDisplay] 翻译歌词获取成功');
            return tlyric;
          }
        }
      }

      globalTalker.info('[LyricDisplay] 无翻译歌词');
      return null;

    } catch (e) {
      globalTalker.error('[LyricDisplay] 翻译歌词获取异常: $e');
      return null;
    }
  }

  /// 切换翻译显示状态
  void toggleTranslation() {
    setState(() {
      _showTranslation = !_showTranslation;
      _updateLyricModel();
    });
  }

  @override
  void dispose() {
    stream.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 歌词翻译按钮
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CupertinoButton(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minSize: 0,
                onPressed: () {
                  setState(() {
                    _showTranslation = !_showTranslation;
                    _updateLyricModel();
                  });
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showTranslation ? CupertinoIcons.eye_slash : CupertinoIcons.eye,
                      size: 16,
                      color: widget.isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      "显示翻译",
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                      ).useSystemChineseFont(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // 歌词显示区域
        Expanded(
          child: Obx(() => LyricsReader(
                  playing: globalAudioHandler.playingMusic.value != null,
                  emptyBuilder: () => Center(
                    child: Text(
                      "暂无歌词",
                      style: TextStyle(
                        color: widget.isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                        fontSize: 16,
                      ).useSystemChineseFont(),
                    ),
                  ),
                  model: lyricModel,
                  position: globalAudioUiController.position.value.inMilliseconds,
                  lyricUi: lyricUI,
                  size: Size(double.infinity, widget.maxHeight), // 使用全部高度
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  selectLineBuilder: (progress, confirm) {
                    return Row(
                      children: [
                        IconButton(
                            onPressed: () {
                              var toSeek = Duration(milliseconds: progress);
                              globalAudioHandler.seek(toSeek).then((value) {
                                confirm.call();
                                // 这里是考虑到在暂停状态下。需要开启播放
                                if (!globalAudioHandler.isPlaying) {
                                  globalAudioHandler.play();
                                }
                              });
                            },
                            icon: const Icon(Icons.play_arrow,
                                color: CupertinoColors.white)),
                        Expanded(
                          child: Container(
                            decoration:
                                const BoxDecoration(color: CupertinoColors.white),
                            height: 1,
                            width: double.infinity,
                          ),
                        ),
                        Text(
                          formatDuration(Duration(milliseconds: progress).inSeconds),
                          style: const TextStyle(color: CupertinoColors.white)
                              .useSystemChineseFont(),
                        )
                      ],
                    );
                  },
                )),
        ),
      ],
    );
  }
}
