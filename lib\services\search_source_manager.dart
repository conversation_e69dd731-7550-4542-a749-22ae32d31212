import 'package:shared_preferences/shared_preferences.dart';
import 'package:app_rhyme/services/js_music_source_service.dart';
import 'package:app_rhyme/src/rust/api/bind/factory_bind.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/utils/const_vars.dart';
import 'package:app_rhyme/utils/global_vars.dart';

/// 搜索源类型
enum SearchSourceType {
  musicApi,  // 内置music_api
  jsSource,  // JS音乐源
}

/// 搜索源信息
class SearchSource {
  final String id;
  final String name;
  final SearchSourceType type;
  final String? description;

  const SearchSource({
    required this.id,
    required this.name,
    required this.type,
    this.description,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchSource &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// 搜索源管理器
class SearchSourceManager {
  static final SearchSourceManager _instance = SearchSourceManager._internal();
  factory SearchSourceManager() => _instance;
  SearchSourceManager._internal();

  static const String _selectedSourceKey = 'selected_search_source';
  
  SearchSource? _selectedSource;
  List<SearchSource> _availableSources = [];

  /// 获取当前选中的搜索源
  SearchSource? get selectedSource => _selectedSource;

  /// 获取可用的搜索源列表
  List<SearchSource> get availableSources => List.unmodifiable(_availableSources);

  /// 初始化搜索源管理器
  Future<void> initialize() async {
    await _loadAvailableSources();
    await _loadSelectedSource();
  }

  /// 加载可用的搜索源
  Future<void> _loadAvailableSources() async {
    _availableSources.clear();

    // 添加内置music_api源
    _availableSources.add(const SearchSource(
      id: 'music_api_netease',
      name: '网易云音乐 (内置)',
      type: SearchSourceType.musicApi,
      description: '使用内置music_api的网易云音乐搜索',
    ));

    // 添加JS音乐源
    final jsService = JsMusicSourceService();
    await jsService.initialize();
    final jsSources = jsService.getAvailableSearchSources();
    
    for (int i = 0; i < jsSources.length; i++) {
      _availableSources.add(SearchSource(
        id: 'js_source_$i',
        name: '${jsSources[i]} (JS源)',
        type: SearchSourceType.jsSource,
        description: '使用JS音乐源: ${jsSources[i]}',
      ));
    }

    globalTalker.info('[SearchSourceManager] Loaded ${_availableSources.length} search sources');
  }

  /// 加载用户选择的搜索源
  Future<void> _loadSelectedSource() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedId = prefs.getString(_selectedSourceKey);
      
      if (selectedId != null) {
        _selectedSource = _availableSources.firstWhere(
          (source) => source.id == selectedId,
          orElse: () => _availableSources.isNotEmpty ? _availableSources.first : 
            const SearchSource(
              id: 'music_api_netease',
              name: '网易云音乐 (内置)',
              type: SearchSourceType.musicApi,
            ),
        );
      } else {
        // 默认选择第一个可用源
        _selectedSource = _availableSources.isNotEmpty ? _availableSources.first : 
          const SearchSource(
            id: 'music_api_netease',
            name: '网易云音乐 (内置)',
            type: SearchSourceType.musicApi,
          );
      }
      
      globalTalker.info('[SearchSourceManager] Selected source: ${_selectedSource?.name}');
    } catch (e) {
      globalTalker.error('[SearchSourceManager] Failed to load selected source: $e');
      _selectedSource = _availableSources.isNotEmpty ? _availableSources.first : null;
    }
  }

  /// 设置选中的搜索源
  Future<void> setSelectedSource(SearchSource source) async {
    try {
      _selectedSource = source;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_selectedSourceKey, source.id);
      globalTalker.info('[SearchSourceManager] Selected source changed to: ${source.name}');
    } catch (e) {
      globalTalker.error('[SearchSourceManager] Failed to save selected source: $e');
    }
  }

  /// 使用当前选中的搜索源进行搜索
  Future<List<MusicAggregatorW>> searchMusic({
    required String keyword,
    required int page,
    required int limit,
    MusicFuzzFilter? filter,
    List<MusicAggregatorW>? existingAggregators,
  }) async {
    if (_selectedSource == null) {
      globalTalker.warning('[SearchSourceManager] No search source selected');
      return [];
    }

    try {
      switch (_selectedSource!.type) {
        case SearchSourceType.musicApi:
          return await _searchWithMusicApi(
            keyword: keyword,
            page: page,
            limit: limit,
            filter: filter,
            existingAggregators: existingAggregators ?? [],
          );
        
        case SearchSourceType.jsSource:
          return await _searchWithJsSource(
            keyword: keyword,
            page: page,
            limit: limit,
          );
      }
    } catch (e) {
      globalTalker.error('[SearchSourceManager] Search failed with ${_selectedSource!.name}: $e');
      return [];
    }
  }

  /// 使用music_api搜索
  Future<List<MusicAggregatorW>> _searchWithMusicApi({
    required String keyword,
    required int page,
    required int limit,
    MusicFuzzFilter? filter,
    required List<MusicAggregatorW> existingAggregators,
  }) async {
    try {
      final results = await AggregatorOnlineFactoryW.searchMusicAggregator(
        aggregators: existingAggregators,
        sources: [sourceNetease],
        content: keyword,
        page: page,
        limit: limit,
        filter: filter,
      );

      globalTalker.info('[SearchSourceManager] Music API search returned ${results.length} results');
      return results;
    } catch (e) {
      globalTalker.error('[SearchSourceManager] Music API search error: $e');
      // 如果music_api搜索失败，尝试使用JS源作为备用
      return await _searchWithJsSource(
        keyword: keyword,
        page: page,
        limit: limit,
      );
    }
  }

  /// 使用JS源搜索
  Future<List<MusicAggregatorW>> _searchWithJsSource({
    required String keyword,
    required int page,
    required int limit,
  }) async {
    final jsService = JsMusicSourceService();
    final musicInfos = await jsService.searchMusic(keyword, page: page, limit: limit);
    
    // 将MusicInfo转换为MusicAggregatorW
    // 这里需要通过Rust后端来创建MusicAggregatorW
    // 暂时返回空列表，需要进一步实现
    globalTalker.info('[SearchSourceManager] JS source search returned ${musicInfos.length} MusicInfo results');
    globalTalker.warning('[SearchSourceManager] MusicInfo to MusicAggregatorW conversion not implemented yet');
    
    return [];
  }

  /// 刷新可用搜索源
  Future<void> refreshSources() async {
    await _loadAvailableSources();
    
    // 检查当前选中的源是否还存在
    if (_selectedSource != null && 
        !_availableSources.any((source) => source.id == _selectedSource!.id)) {
      // 如果当前选中的源不存在了，选择第一个可用源
      if (_availableSources.isNotEmpty) {
        await setSelectedSource(_availableSources.first);
      } else {
        _selectedSource = null;
      }
    }
  }
}
