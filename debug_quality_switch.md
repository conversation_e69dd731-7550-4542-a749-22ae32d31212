# 音质切换调试指南

## 问题分析

用户反馈从 hires 切换到无损音质时，结果还是变成了 hires。

## 修复内容

### 1. 修复了 `_updateQuality` 方法
- **问题**: 当调用 `replacePlayingMusic(quality)` 时，直接使用传入的音质，忽略了临时音质设置
- **修复**: 即使有明确的音质参数，也会先检查临时音质设置

### 2. 添加了详细的调试日志
- 临时音质设置日志
- 音质选择过程日志
- 音质更新过程日志

## 调试步骤

### 1. 查看日志输出
当你切换音质时，应该看到以下日志：

```
[QualityTime] 设置临时音质: netease_歌曲名_歌手名 -> lossless
[TemporaryQualityManager] 设置临时音质: netease_歌曲名_歌手名 -> lossless
[MusicContainer] 使用临时音质: netease_歌曲名_歌手名 -> lossless
```

### 2. 检查音质切换流程

1. **点击音质按钮** → 显示音质选择菜单
2. **选择新音质** → 设置临时音质 + 调用 replacePlayingMusic
3. **_updateQuality 被调用** → 检查临时音质设置 → 使用临时音质
4. **播放新音质的音乐**

### 3. 验证临时音质是否生效

检查以下几点：
- [ ] 临时音质是否正确设置到管理器中
- [ ] `_updateQuality` 是否检查到临时音质
- [ ] 是否在可用音质列表中找到匹配的音质对象
- [ ] 最终使用的音质是否是用户选择的音质

## 可能的问题和解决方案

### 问题1: 音质名称不匹配
**症状**: 设置了临时音质但没有生效
**原因**: JS源返回的音质名称与用户选择的不一致
**解决**: 检查 `quality.short` 的值是否与临时设置的名称匹配

### 问题2: 音质对象未找到
**症状**: 临时音质设置了但找不到对应的Quality对象
**原因**: 在 `info.qualities` 中没有找到匹配的音质
**解决**: 确保JS源提供的音质列表包含用户选择的音质

### 问题3: 音质切换时机问题
**症状**: 音质设置了但播放的还是旧音质
**原因**: 音质更新在播放资源更新之后
**解决**: 确保 `_updateQuality` 在 `replacePlayingMusic` 过程中被正确调用

## 测试用例

### 测试1: 基本音质切换
1. 播放一首歌（默认音质）
2. 点击音质按钮，选择不同音质
3. 验证播放的音质是否改变

### 测试2: 临时音质持久性
1. 切换音质
2. 暂停/播放
3. 验证音质是否保持

### 测试3: 临时音质清理
1. 切换音质
2. 切换到下一首歌
3. 验证新歌是否使用默认音质

### 测试4: 设置页面不受影响
1. 切换播放页面的音质
2. 检查设置页面的默认音质
3. 验证设置页面的音质没有改变

## 日志关键词

搜索以下关键词来追踪问题：
- `[QualityTime]` - 音质切换UI相关
- `[TemporaryQualityManager]` - 临时音质管理
- `[MusicContainer]` - 音质更新过程
- `[QualityPicker]` - 音质选择逻辑

## 预期行为

1. **用户选择音质** → 临时音质被设置
2. **音乐重新加载** → 使用临时音质而不是默认音质
3. **切换歌曲** → 临时音质被清除，新歌使用默认音质
4. **设置页面** → 默认音质不受影响
