import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lyric/lyric_ui/lyric_ui.dart';

class AppleMusicLyricUi extends LyricUI {
  AppleMusicLyricUi();

  @override
  TextStyle getPlayingMainTextStyle() {
    return const TextStyle(
      color: Color.fromARGB(255, 120, 119, 118),
      fontSize: 36,
      fontWeight: FontWeight.bold,
    ).useSystemChineseFont();
  }

  // 翻译歌词 - 当前播放行的样式，与主歌词保持一致的字体和大小
  @override
  TextStyle getPlayingExtTextStyle() {
    return const TextStyle(
      color: Color.fromARGB(255, 120, 119, 118), // 与主歌词相同颜色
      fontSize: 22, // 与其他主歌词相同大小
      fontWeight: FontWeight.bold, // 与主歌词相同粗细
    ).useSystemChineseFont();
  }

  @override
  TextStyle getOtherMainTextStyle() {
    return const TextStyle(
      color: Color.fromARGB(200, 120, 119, 118),
      fontSize: 22,
      fontWeight: FontWeight.bold,
    ).useSystemChineseFont();
  }

  // 翻译歌词 - 其他行的样式，与其他主歌词保持一致
  @override
  TextStyle getOtherExtTextStyle() {
    return const TextStyle(
      color: Color.fromARGB(200, 120, 119, 118), // 与其他主歌词相同颜色
      fontSize: 22, // 与其他主歌词相同大小
      fontWeight: FontWeight.bold, // 与其他主歌词相同粗细
    ).useSystemChineseFont();
  }

  @override
  double getBlankLineHeight() => 16;

  @override
  double getLineSpace() => 26;

  @override
  double getInlineSpace() => 8;

  @override
  double getPlayingLineBias() => 0.4;

  @override
  LyricAlign getLyricHorizontalAlign() => LyricAlign.LEFT;

  @override
  bool enableLineAnimation() => true;

  @override
  bool enableHighlight() => true;

  @override
  Color getLyricHightlightColor() {
    return const Color.fromARGB(255, 255, 255, 255);
  }

  @override
  bool initAnimation() => true;
}
