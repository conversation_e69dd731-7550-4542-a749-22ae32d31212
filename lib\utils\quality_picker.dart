import 'dart:io';

import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/types/chore.dart';
import 'package:app_rhyme/utils/global_vars.dart';

enum QualityOption { low, medium, high, highest }

String qualityOptionToString(QualityOption qualityOption) {
  switch (qualityOption) {
    case QualityOption.low:
      return "最低";
    case QualityOption.medium:
      return "中等";
    case QualityOption.high:
      return "较高";
    case QualityOption.highest:
      return "最高";
  }
}

QualityOption stringToQualityOption(String qualityOptionString) {
  switch (qualityOptionString) {
    case "最低":
      return QualityOption.low;
    case "中等":
      return QualityOption.medium;
    case "较高":
      return QualityOption.high;
    case "最高":
      return QualityOption.highest;
    default:
      return QualityOption.medium;
  }
}

Quality autoPickQualityByOption(List<Quality> qualities, QualityOption option) {
  if (qualities.length == 1) {
    return qualities.first;
  } else if (qualities.length == 2) {
    switch (option) {
      case QualityOption.low:
        return qualities.last;
      case QualityOption.medium:
        return qualities.last;
      case QualityOption.high:
        return qualities.first;
      case QualityOption.highest:
        return qualities.first;
    }
  } else if (qualities.length == 3) {
    switch (option) {
      case QualityOption.low:
        return qualities.last;
      case QualityOption.medium:
        return qualities[1];
      case QualityOption.high:
        return qualities[1];
      case QualityOption.highest:
        return qualities.first;
    }
  } else if (qualities.length == 4) {
    switch (option) {
      case QualityOption.low:
        return qualities[3];
      case QualityOption.medium:
        return qualities[2];
      case QualityOption.high:
        return qualities[1];
      case QualityOption.highest:
        return qualities[0];
    }
  } else {
    switch (option) {
      case QualityOption.low:
        return qualities.last;
      case QualityOption.medium:
        return qualities[(qualities.length / 2).ceil()];
      case QualityOption.high:
        return qualities[(qualities.length * 3 / 4).ceil()];
      case QualityOption.highest:
        return qualities.first;
    }
  }
}

Quality autoPickQuality(List<Quality> qualities, {String? musicId}) {
  // 如果有临时音质设置，优先使用临时音质
  if (musicId != null && globalTemporaryQualityManager.hasTemporaryQuality(musicId)) {
    final temporaryQuality = globalTemporaryQualityManager.getTemporaryQuality(musicId)!;
    globalTalker.info('[QualityPicker] 使用临时音质: $musicId -> $temporaryQuality');
    return autoPickQualityByName(qualities, temporaryQuality);
  }

  // 否则使用默认音质设置
  String selectedQuality;
  if (Platform.isIOS || Platform.isAndroid) {
    switch (globalConnectivityStateSimple) {
      case ConnectivityStateSimple.wifi:
        selectedQuality = globalConfig.wifiAutoQuality;
        break;
      case ConnectivityStateSimple.mobile:
        selectedQuality = globalConfig.mobileAutoQuality;
        break;
      case ConnectivityStateSimple.none:
        selectedQuality = 'lossless';
        break;
    }
  } else {
    selectedQuality = globalConfig.wifiAutoQuality;
  }

  // 减少日志输出，只在调试时输出
  // if (musicId != null) {
  //   globalTalker.info('[QualityPicker] 使用默认音质: $musicId -> $selectedQuality');
  // }
  return autoPickQualityByName(qualities, selectedQuality);
}

/// 根据音质名称选择音质（优先使用JS源音质名称）
Quality autoPickQualityByName(List<Quality> qualities, String qualityName) {
  if (qualities.isEmpty) {
    throw ArgumentError('qualities list cannot be empty');
  }

  // 首先尝试直接匹配JS源音质名称
  for (final quality in qualities) {
    if (quality.short == qualityName) {
      return quality;
    }
  }

  // 如果没有找到直接匹配，尝试匹配显示名称
  for (final quality in qualities) {
    if (quality.level == qualityName) {
      return quality;
    }
  }

  // 如果仍然没有找到，使用传统的QualityOption逻辑作为后备
  final qualityOption = stringToQualityOption(qualityName);
  return autoPickQualityByOption(qualities, qualityOption);
}
