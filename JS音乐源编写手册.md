# AppRhyme JS音乐源编写手册

## 概述

AppRhyme支持通过JavaScript编写自定义音乐源，用于扩展音乐搜索和播放功能。本手册将详细介绍如何编写、配置和部署JS音乐源。

## 基本结构

### 1. 配置信息 (CONFIG)

每个JS音乐源必须包含一个CONFIG对象，定义音乐源的基本信息：

```javascript
const CONFIG = {
    name: "音乐源名称",
    version: "1.0.0",
    author: "作者名称",
    description: "音乐源描述",
    supportedQualities: ["128k", "320k", "flac", "hires"]
};
```

**字段说明：**
- `name`: 音乐源显示名称
- `version`: 版本号，建议使用语义化版本
- `author`: 作者信息
- `description`: 详细描述
- `supportedQualities`: 支持的音质列表

**支持的音质类型：**
- `128k`: 标准音质 (128kbps)
- `320k`: 高品质 (320kbps)
- `flac`: 无损音质 (FLAC格式)
- `hires`: 高解析度音质
- `surround`: 环绕声
- `sky`: 天空音质
- `master`: 母带音质

### 2. 核心函数

#### getPlayInfo(musicInfo, quality)

**最重要的函数**，用于获取音乐播放链接和相关信息。

```javascript
async function getPlayInfo(musicInfo, quality = "320k") {
    try {
        // 1. 解析音乐ID
        let musicId = extractMusicId(musicInfo);
        
        // 2. 构建API请求
        const apiUrl = `https://api.example.com/music?id=${musicId}&quality=${quality}`;
        
        // 3. 发送HTTP请求
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://music.example.com/'
            }
        });
        
        // 4. 解析响应
        const data = await response.json();
        
        // 5. 返回标准格式
        return {
            uri: data.playUrl,
            quality: {
                short: quality,
                level: data.qualityLevel,
                bitrate: data.bitrate,
                format: data.format,
                size: data.fileSize
            },
            title: data.songName,
            artist: data.artistName,
            album: data.albumName,
            pic: data.coverUrl,
            lyric: data.lyric,
            tlyric: data.translatedLyric
        };
        
    } catch (error) {
        console.error("[MusicSource] 获取播放信息失败:", error);
        return null;
    }
}
```

**参数说明：**
- `musicInfo`: 音乐信息对象，包含id、name、artist、album等字段
- `quality`: 请求的音质，如"128k"、"320k"等

**返回格式：**
```javascript
{
    uri: "播放链接",
    quality: {
        short: "音质简称",
        level: "音质描述",
        bitrate: 比特率数字,
        format: "音频格式",
        size: "文件大小"
    },
    title: "歌曲名称",
    artist: "艺术家",
    album: "专辑名称",
    pic: "封面图片URL",
    lyric: "歌词内容",
    tlyric: "翻译歌词"
}
```

#### searchMusic(keyword, page, limit) [可选]

用于搜索音乐，目前AppRhyme主要使用内置搜索，此函数为扩展功能。

```javascript
async function searchMusic(keyword, page = 1, limit = 20) {
    try {
        const searchUrl = `https://api.example.com/search?q=${encodeURIComponent(keyword)}&page=${page}&limit=${limit}`;
        
        const response = await fetch(searchUrl);
        const data = await response.json();
        
        return data.results.map(item => ({
            id: item.id.toString(),
            name: item.title,
            artist: item.artists.map(a => a.name),
            album: item.album.name,
            duration: item.duration,
            pic: item.cover,
            url: item.shareUrl,
            extra: JSON.stringify({
                source_id: item.id,
                album_id: item.album.id
            })
        }));
        
    } catch (error) {
        console.error("[MusicSource] 搜索失败:", error);
        return [];
    }
}
```

#### getLyric(musicInfo) [可选]

获取歌词信息。

```javascript
async function getLyric(musicInfo) {
    try {
        const lyricUrl = `https://api.example.com/lyric?id=${musicInfo.id}`;
        const response = await fetch(lyricUrl);
        const data = await response.json();
        return data.lyric;
    } catch (error) {
        console.error("[MusicSource] 获取歌词失败:", error);
        return null;
    }
}
```

#### validateSource() [可选]

验证音乐源是否可用。

```javascript
async function validateSource() {
    try {
        const testUrl = "https://api.example.com/status";
        const response = await fetch(testUrl);
        return response.ok;
    } catch (error) {
        return false;
    }
}
```

## 实用工具函数

### 音乐ID提取

```javascript
function extractMusicId(musicInfo) {
    // 1. 优先从extra字段获取
    if (musicInfo.extra) {
        try {
            const extraData = JSON.parse(musicInfo.extra);
            if (extraData.source_id) {
                return extraData.source_id.toString();
            }
        } catch (e) {
            console.log("解析extra字段失败:", e);
        }
    }
    
    // 2. 从URL中提取
    if (musicInfo.url) {
        const idMatch = musicInfo.url.match(/id[=\/](\d+)/);
        if (idMatch) {
            return idMatch[1];
        }
    }
    
    // 3. 使用内部ID
    if (musicInfo.id && musicInfo.id !== '0') {
        return musicInfo.id.toString();
    }
    
    return null;
}
```

### 时长解析

```javascript
function parseDuration(durationStr) {
    if (!durationStr) return 0;
    
    const parts = durationStr.split(':');
    if (parts.length === 2) {
        return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    } else if (parts.length === 3) {
        return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    }
    
    return 0;
}
```

### 音质映射

```javascript
const QUALITY_MAP = {
    "128k": "standard",
    "320k": "exhigh",
    "flac": "lossless",
    "hires": "hires"
};

function mapQualityToApi(quality) {
    return QUALITY_MAP[quality] || "exhigh";
}
```

## 错误处理

### 1. 网络请求错误

```javascript
async function safeRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            timeout: 10000, // 10秒超时
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`请求失败 ${url}:`, error);
        throw error;
    }
}
```

### 2. 数据验证

```javascript
function validatePlayInfo(playInfo) {
    if (!playInfo || !playInfo.uri) {
        throw new Error("无效的播放信息：缺少播放链接");
    }
    
    if (!playInfo.quality || !playInfo.quality.short) {
        throw new Error("无效的播放信息：缺少音质信息");
    }
    
    return true;
}
```

## 模块导出

为了兼容不同环境，需要正确导出模块：

```javascript
// Node.js环境
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        getPlayInfo,
        searchMusic,
        getLyric,
        validateSource
    };
}

// 浏览器环境
if (typeof window !== 'undefined') {
    window.MusicSourceName = {
        CONFIG,
        getPlayInfo,
        searchMusic,
        getLyric,
        validateSource
    };
}
```

## 调试和日志

### 1. 控制台日志

```javascript
// 使用统一的日志前缀
const LOG_PREFIX = "[MusicSource]";

function log(level, message, ...args) {
    console[level](`${LOG_PREFIX} ${message}`, ...args);
}

// 使用示例
log('info', '开始获取播放信息:', musicInfo.name);
log('error', '请求失败:', error);
log('warn', '音质不支持，使用默认音质');
```

### 2. 性能监控

```javascript
async function getPlayInfoWithTiming(musicInfo, quality) {
    const startTime = Date.now();
    
    try {
        const result = await getPlayInfo(musicInfo, quality);
        const duration = Date.now() - startTime;
        console.log(`${LOG_PREFIX} 获取播放信息耗时: ${duration}ms`);
        return result;
    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`${LOG_PREFIX} 获取播放信息失败 (${duration}ms):`, error);
        throw error;
    }
}
```

## 最佳实践

### 1. 请求头设置

```javascript
const DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache'
};
```

### 2. 重试机制

```javascript
async function requestWithRetry(url, options = {}, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await fetch(url, options);
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            
            const delay = Math.pow(2, i) * 1000; // 指数退避
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

### 3. 缓存策略

```javascript
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

function getCachedData(key) {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.data;
    }
    return null;
}

function setCachedData(key, data) {
    cache.set(key, {
        data: data,
        timestamp: Date.now()
    });
}
```

## 部署和使用

### 1. 文件托管

将JS文件上传到可访问的HTTP服务器，如：
- GitHub Pages
- Gitee Pages  
- 自建服务器
- CDN服务

### 2. 在AppRhyme中添加

1. 打开AppRhyme应用
2. 进入"更多"页面
3. 找到"JS音乐源管理"
4. 点击"+"添加新源
5. 输入JS文件的URL
6. 等待加载完成

### 3. 测试验证

添加成功后，可以通过以下方式测试：
1. 查看音乐源是否正确加载
2. 尝试播放音乐，检查是否能获取播放链接
3. 检查音质切换是否正常
4. 查看日志输出是否正常

## 注意事项

### 1. 法律合规
- 确保遵守相关法律法规
- 尊重版权，仅用于个人学习和测试
- 不得用于商业用途

### 2. 技术限制
- JS代码在沙箱环境中运行
- 网络请求受CORS限制
- 不支持Node.js特有的API

### 3. 性能优化
- 避免同步阻塞操作
- 合理使用缓存
- 控制请求频率
- 及时释放资源

### 4. 错误处理
- 所有异步操作都要有错误处理
- 返回null而不是抛出异常
- 提供有意义的错误信息

## 示例模板

```javascript
/**
 * 音乐源模板
 */

const CONFIG = {
    name: "示例音乐源",
    version: "1.0.0", 
    author: "开发者",
    description: "这是一个示例音乐源",
    supportedQualities: ["128k", "320k", "flac"]
};

async function getPlayInfo(musicInfo, quality = "320k") {
    try {
        // TODO: 实现播放信息获取逻辑
        return {
            uri: "播放链接",
            quality: {
                short: quality,
                level: "音质描述",
                bitrate: 320,
                format: "mp3",
                size: null
            },
            title: musicInfo.name,
            artist: Array.isArray(musicInfo.artist) ? musicInfo.artist.join(',') : musicInfo.artist,
            album: musicInfo.album,
            pic: musicInfo.pic,
            lyric: null,
            tlyric: null
        };
    } catch (error) {
        console.error("[示例音乐源] 获取播放信息失败:", error);
        return null;
    }
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, getPlayInfo };
}

if (typeof window !== 'undefined') {
    window.ExampleMusicSource = { CONFIG, getPlayInfo };
}

console.log("[示例音乐源] 已加载");
```

这个模板提供了最基本的结构，开发者可以基于此进行扩展和定制。
