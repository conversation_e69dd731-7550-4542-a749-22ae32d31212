## MusicApi

# 功能

- 音乐信息搜索
- 音乐信息储存

注意：本仓库只具备公开的音乐信息的搜索功能，无力提供音乐试听，下载等功能。

# 数据源

本仓库所有数据均从各官方平台的公开数据库中获取，所获得数据和未登录状态下官方平台数据相同，仅对数据做简单处理和抽象，因此本项目无力为数据的合法性和正确性负责。

# 版权

本仓库代码运行中可能会产生版权数据，但均为各官方平台公开数据，数据所有权归各官方平台所有。**为避免侵权，请使用者务必在24小时内清除本项目的版权数据**；

音乐创作不易，请保护版权，支持正版。

# 免责声明

由于使用本仓库产生的包括由于使用本项目而引起的任何性质的任何直接、间接、特殊、偶然或结果性损害（包括但不限于因商誉损失、停工、计算机故障或故障引起的损害赔偿，或任何及所有其他商业损害或损失等）由使用者负责。

# 使用限制

1. 使用本仓库代码的使用者必须接受本项目的协议和免责声明。
2. 务必在当地法律的允许范围内使用本项目，由于Github和音乐的全球性，本项目无法保证符合世界各国各地区法律规定。对于违法当地法律的使用者，造成的一切违法违规由使用者自行承担责任，本项目不承担由此造成的任何直接、间接、特殊、偶然或结果性责任。

# 协议
MIT or Apache-2.0

# 贡献声明
本项目不接受任何商业合作，不接受任何商业捐赠。