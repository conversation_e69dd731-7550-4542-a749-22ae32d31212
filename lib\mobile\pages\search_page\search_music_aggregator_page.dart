import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/factory_bind.dart';
import 'package:app_rhyme/services/js_music_source_service.dart';
import 'package:app_rhyme/types/music_container.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/music_api_helper.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

const String sourceNetease = "WangYi";

class SearchMusicAggregatorPage extends StatefulWidget {
  const SearchMusicAggregatorPage({super.key});

  @override
  _SearchMusicAggregatorPageState createState() =>
      _SearchMusicAggregatorPageState();
}

class _SearchMusicAggregatorPageState extends State<SearchMusicAggregatorPage>
    with WidgetsBindingObserver {
  final PagingController<int, MusicInfo> _pagingController =
      PagingController(firstPageKey: 1);
  final TextEditingController _inputContentController = TextEditingController();
  final Set<int> _favoriteIds = <int>{}; // 记录已收藏的歌曲ID
  final JsMusicSourceService _jsService = JsMusicSourceService();

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _pagingController.addPageRequestListener((pageKey) {
      _fetchMusicInfos(pageKey);
    });
    _initializeJsService();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pagingController.dispose();
    _inputContentController.dispose();
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    setState(() {});
  }

  Future<void> _initializeJsService() async {
    try {
      await _jsService.initialize();
    } catch (e) {
      LogToast.error(
        "初始化失败",
        "JS音乐源初始化失败: $e",
        "[SearchMusicAggregatorPage] JS service init failed: $e",
      );
    }
  }

  Future<void> _fetchMusicInfos(int pageKey) async {
    try {
      if (_inputContentController.value.text.isEmpty) {
        _pagingController.appendLastPage([]);
        return;
      }

      // 使用JS音乐源进行搜索
      final results = await _jsService.searchMusic(
        _inputContentController.value.text,
        page: pageKey,
        limit: 30,
      );

      final isLastPage = results.length < 30;
      if (isLastPage) {
        _pagingController.appendLastPage(results);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(results, nextPageKey);
      }
    } catch (error) {
      LogToast.error(
        "搜索失败",
        "搜索音乐失败: $error",
        "[SearchMusicAggregatorPage] Search failed: $error",
      );
      _pagingController.error = error;
    }
  }



  void _clearSearch() {
    setState(() {
      _inputContentController.clear(); // 清空搜索框
    });
    _pagingController.refresh();
  }

  /// 播放音乐
  Future<void> _playMusic(MusicInfo musicInfo) async {
    try {
      // 使用JS音乐源直接获取播放链接并播放
      // 这样避免了复杂的MusicAggregatorW创建过程
      await _playMusicDirectly(musicInfo);
    } catch (e) {
      LogToast.error(
        "播放失败",
        "播放音乐失败: $e",
        "[SearchMusicAggregatorPage] Play failed: $e",
      );
    }
  }

  /// 直接使用JS音乐源播放音乐
  Future<void> _playMusicDirectly(MusicInfo musicInfo) async {
    try {
      // 使用搜索方法获取MusicAggregatorW，然后播放
      // 这样可以复用现有的播放逻辑
      final aggregators = await AggregatorOnlineFactoryW.searchMusicAggregator(
        aggregators: [],
        sources: ["WangYi"], // 伪装成WangYi源，和歌单页保持一致
        content: "${musicInfo.name} ${musicInfo.artist.join(' ')}", // 搜索歌名+歌手
        page: 1,
        limit: 1,
      );

      if (aggregators.isNotEmpty) {
        final container = MusicContainer(aggregators.first);
        // 设置extra信息，让MusicContainer使用JS音乐源
        container.extra = musicInfo.id.toString();
        await globalAudioHandler.addMusicPlay(container);
        LogToast.info("播放音乐", "正在播放: ${musicInfo.name}", "");
      } else {
        throw Exception("未找到匹配的音乐");
      }

    } catch (e) {
      throw Exception("直接播放失败: $e");
    }
  }

  /// 添加到收藏
  Future<void> _addToFavorites(MusicInfo musicInfo) async {
    try {
      // 使用和歌单页相同的方法：通过搜索获取MusicAggregatorW，然后添加到收藏
      final aggregators = await AggregatorOnlineFactoryW.searchMusicAggregator(
        aggregators: [],
        sources: ["WangYi"], // 伪装成WangYi源，和歌单页保持一致
        content: "${musicInfo.name} ${musicInfo.artist.join(' ')}", // 搜索歌名+歌手
        page: 1,
        limit: 1,
      );

      if (aggregators.isNotEmpty) {
        // 获取"我的喜欢歌单"
        final allLists = await SqlFactoryW.getAllMusiclists();
        final favoriteList = allLists.where(
          (list) => list.getMusiclistInfo().name == "我的喜欢歌单"
        ).firstOrNull;

        if (favoriteList != null) {
          await SqlFactoryW.addMusics(
            musicsListName: "我的喜欢歌单",
            musics: [aggregators.first],
          );
          // 更新收藏状态
          setState(() {
            _favoriteIds.add(musicInfo.id);
          });
          LogToast.success(
            "添加成功",
            "已添加到我的喜欢歌单",
            "[SearchMusicAggregatorPage] Added to favorite playlist"
          );
        } else {
          LogToast.error(
            "添加失败",
            "我的喜欢歌单不存在",
            "[SearchMusicAggregatorPage] Favorite playlist not found"
          );
        }
      } else {
        LogToast.error("添加失败", "未找到匹配的音乐", "");
      }
    } catch (e) {
      LogToast.error(
        "添加失败",
        "添加到收藏失败: $e",
        "[SearchMusicAggregatorPage] Add to favorites failed: $e",
      );
    }
  }



  /// 添加到歌单
  Future<void> _addToPlaylist(MusicInfo musicInfo) async {
    try {
      // 使用和桌面版搜索页相同的方法：通过搜索获取MusicAggregatorW
      final aggregators = await AggregatorOnlineFactoryW.searchMusicAggregator(
        aggregators: [],
        sources: ["WangYi"], // 伪装成WangYi源，和歌单页保持一致
        content: "${musicInfo.name} ${musicInfo.artist.join(' ')}", // 搜索歌名+歌手
        page: 1,
        limit: 1,
      );

      if (aggregators.isNotEmpty) {
        final container = MusicContainer(aggregators.first);
        if (mounted) {
          await addMusicsToMusicList(context, [container]);
        }
      } else {
        LogToast.error("添加失败", "未找到匹配的音乐", "");
      }
    } catch (e) {
      LogToast.error(
        "添加失败",
        "添加到歌单失败: $e",
        "[SearchMusicAggregatorPage] Add to playlist failed: $e",
      );
    }
  }

  /// 构建MusicInfo列表项
  Widget _buildMusicInfoItem(MusicInfo musicInfo, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 2),
      child: Row(
        children: [
          // 专辑封面
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
            ),
            child: musicInfo.artPic != null && musicInfo.artPic!.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      musicInfo.artPic!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          CupertinoIcons.music_note,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        );
                      },
                    ),
                  )
                : Icon(
                    CupertinoIcons.music_note,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
          ),
          const SizedBox(width: 12),
          // 歌曲信息 - 整个区域支持长按复制
          Expanded(
            child: GestureDetector(
              onLongPress: () {
                Clipboard.setData(ClipboardData(text: musicInfo.name));
                showCupertinoDialog(
                  context: context,
                  builder: (context) => CupertinoAlertDialog(
                    title: Text('已复制', style: const TextStyle().useSystemChineseFont()),
                    content: Text('歌曲名称已复制到剪贴板', style: const TextStyle().useSystemChineseFont()),
                    actions: [
                      CupertinoDialogAction(
                        child: Text('确定', style: const TextStyle().useSystemChineseFont()),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    musicInfo.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ).useSystemChineseFont(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    musicInfo.artist.join(', '),
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ).useSystemChineseFont(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          // 操作按钮
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 收藏按钮
              CupertinoButton(
                padding: const EdgeInsets.all(8),
                onPressed: () => _addToFavorites(musicInfo),
                child: Icon(
                  _favoriteIds.contains(musicInfo.id)
                    ? CupertinoIcons.heart_fill
                    : CupertinoIcons.heart,
                  size: 20,
                  color: _favoriteIds.contains(musicInfo.id)
                    ? Colors.red
                    : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
                ),
              ),
              // 添加到歌单按钮
              CupertinoButton(
                padding: const EdgeInsets.all(8),
                onPressed: () => _addToPlaylist(musicInfo),
                child: Icon(
                  CupertinoIcons.plus,
                  size: 20,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }








  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    final bool isDarkMode =
        MediaQuery.of(context).platformBrightness == Brightness.dark;

    return CupertinoPageScaffold(
      backgroundColor:
          isDarkMode ? CupertinoColors.black : CupertinoColors.white,
      child: Column(
        children: [
          // 导航栏
          CupertinoNavigationBar(
            backgroundColor:
                isDarkMode ? CupertinoColors.black : CupertinoColors.white,
            leading: Padding(
              padding: const EdgeInsets.only(left: 0.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  '搜索歌曲',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode
                        ? CupertinoColors.white
                        : CupertinoColors.black,
                  ).useSystemChineseFont(),
                ),
              ),
            ),
          ),
          // 搜索框和过滤按钮
          Padding(
            padding: const EdgeInsets.only(top: 4.0, left: 8, right: 8),
            child: Row(
              children: [
                Expanded(
                  child: CupertinoSearchTextField(
                    style: TextStyle(
                      color: isDarkMode
                          ? CupertinoColors.white
                          : CupertinoColors.black,
                    ).useSystemChineseFont(),
                    controller: _inputContentController,
                    onSubmitted: (String value) {
                      if (value.isNotEmpty) {
                        _pagingController.refresh();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8), // 增加间距
                CupertinoButton(
                  padding: const EdgeInsets.all(8),
                  onPressed: _clearSearch,
                  child: const Icon(
                    CupertinoIcons.clear,
                    size: 25,
                  ),
                ),
              ],
            ),
          ),

          // 歌曲列表
          Expanded(
            child: PagedListView(
              pagingController: _pagingController,
              padding: EdgeInsets.only(bottom: screenHeight * 0.1),
              builderDelegate: PagedChildBuilderDelegate<MusicInfo>(
                noItemsFoundIndicatorBuilder: (context) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '输入关键词以搜索单曲',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isDarkMode
                                ? CupertinoColors.systemGrey2
                                : CupertinoColors.black,
                          ).useSystemChineseFont(),
                        ),
                        Text(
                          '使用JS音乐源进行搜索',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isDarkMode
                                ? CupertinoColors.systemGrey2
                                : CupertinoColors.black,
                          ).useSystemChineseFont(),
                        ),
                        Text(
                          '点击输入框右侧按钮进行设置筛选条件',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isDarkMode
                                ? CupertinoColors.systemGrey2
                                : CupertinoColors.black,
                          ).useSystemChineseFont(),
                        ),
                      ],
                    ),
                  );
                },
                itemBuilder: (context, musicInfo, index) => Column(
                  children: [
                    // 使用自定义的MusicInfo列表项
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => _playMusic(musicInfo),
                      child: _buildMusicInfoItem(musicInfo, isDarkMode),
                    ),
                    if (index < (_pagingController.itemList?.length ?? 0) - 1)
                      Center(
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width * 0.85,
                          child: Divider(
                            color: isDarkMode
                                ? CupertinoColors.systemGrey4
                                : CupertinoColors.systemGrey5,
                            height: 0.5,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}


