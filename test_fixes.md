# 修复验证测试

## 测试步骤

### 1. 歌词显示测试
1. 启动应用
2. 播放一首歌曲
3. 进入播放页面的歌词界面
4. 验证：
   - [ ] 歌词能正常显示
   - [ ] 如果原本没有歌词，现在能从JS源获取歌词
   - [ ] 桌面版和移动版都能正常显示歌词

### 2. 封面布局测试
1. 进入播放页面主界面
2. 验证：
   - [ ] 封面与歌曲名称之间有合适的间距
   - [ ] 封面不会太靠近歌曲信息
   - [ ] 整体布局看起来协调

### 3. 音质切换测试
1. 进入播放页面
2. 点击音质按钮
3. 选择不同的音质
4. 验证：
   - [ ] 音质能正常切换
   - [ ] 播放质量确实改变
   - [ ] 设置页面的默认音质没有被修改
5. 切换到下一首歌
6. 验证：
   - [ ] 新歌曲使用默认音质设置
   - [ ] 上一首歌的临时音质设置被清除

### 4. 设置页面音质测试
1. 进入设置页面
2. 修改默认播放音质
3. 播放新歌曲
4. 验证：
   - [ ] 新歌曲使用新的默认音质
   - [ ] 手动切换音质仍然只影响当前歌曲

## 预期结果

- ✅ 歌词能从JS音乐源正确获取和显示
- ✅ 播放页面布局更加美观，封面间距合适
- ✅ 音质切换功能正常，临时设置不影响全局配置
- ✅ 切换歌曲时临时设置自动清理

## 技术验证点

### 歌词获取流程
```
1. 检查 currentMusic.info.lyric
2. 如果为空/无效 -> 调用 _fetchLyricFromJsSource()
3. 调用 currentMusic.aggregator.fetchLyric()
4. 更新歌词并刷新UI
```

### 临时音质管理
```
1. 用户点击音质切换
2. 设置临时音质: globalTemporaryQualityManager.setTemporaryQuality()
3. 切换歌曲时清理: clearTemporaryQuality()
4. 音质选择优先级: 临时设置 > 默认设置
```

### 布局改进
```
封面容器高度: screenHeight * 0.87 - 320
封面底部间距: bottom: 30
```

## 故障排除

如果测试失败，检查：

1. **歌词不显示**：
   - 检查JS音乐源是否正确加载
   - 查看控制台日志中的歌词获取错误信息

2. **音质切换无效**：
   - 确认临时音质管理器已正确初始化
   - 检查音质选择逻辑是否正确调用

3. **布局问题**：
   - 检查屏幕尺寸计算
   - 验证padding和margin设置
