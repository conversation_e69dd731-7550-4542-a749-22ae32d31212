import 'dart:async';
import 'dart:io';

import 'package:app_rhyme/dialogs/confirm_dialog.dart';

import 'package:app_rhyme/dialogs/quality_select_dialog.dart';
import 'package:app_rhyme/dialogs/wait_dialog.dart';

import 'package:app_rhyme/src/rust/api/bind/factory_bind.dart';
import 'package:app_rhyme/src/rust/api/cache/fs_util.dart';
import 'package:app_rhyme/src/rust/api/types/extern_api.dart';
import 'package:app_rhyme/utils/cache_helper.dart';
import 'package:app_rhyme/utils/check_update.dart';
import 'package:app_rhyme/utils/chore.dart';
import 'package:app_rhyme/utils/colors.dart';
import 'package:app_rhyme/utils/const_vars.dart';
import 'package:app_rhyme/pages/js_music_source_management_page.dart';
import 'package:app_rhyme/services/js_music_source_service.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/utils/quality_picker.dart';
import 'package:app_rhyme/utils/refresh.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:talker_flutter/talker_flutter.dart';

/// 显示JS音乐源音质选择对话框
Future<String?> showJsQualitySelectionDialog(BuildContext context) async {
  final jsService = JsMusicSourceService();

  // 确保JS源已初始化
  if (!jsService.isInitialized) {
    await jsService.initialize();
  }

  final availableQualities = jsService.getAvailableQualities();

  if (availableQualities.isEmpty) {
    // 如果没有JS音乐源，使用默认音质选项
    QualityOption? selectedOption = await showQualityOptionDialog(context);
    if (selectedOption != null) {
      return qualityOptionToString(selectedOption);
    }
    return null;
  }

  final Brightness brightness = MediaQuery.of(context).platformBrightness;
  final bool isDarkMode = brightness == Brightness.dark;

  return await showCupertinoModalPopup<String>(
    context: context,
    builder: (BuildContext context) {
      return CupertinoActionSheet(
        title: Text(
          '选择默认播放音质',
          style: TextStyle(
            color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
          ).useSystemChineseFont(),
        ),
        message: Text(
          '音质选项来自已加载的JS音乐源',
          style: TextStyle(
            color: isDarkMode ? CupertinoColors.systemGrey : CupertinoColors.systemGrey2,
          ).useSystemChineseFont(),
        ),
        actions: availableQualities.map((quality) {
          return CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop(quality);
            },
            child: Text(
              _getDisplayQualityName(quality),
              style: const TextStyle().useSystemChineseFont(),
            ),
          );
        }).toList(),
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(
            '取消',
            style: const TextStyle().useSystemChineseFont(),
          ),
        ),
      );
    },
  );
}

/// 获取音质的显示名称
String _getDisplayQualityName(String quality) {
  const qualityNames = {
    'jymaster': '超清母带',
    'master': '超清母带',
    'sky': '沉浸环绕声',
    'hires': 'Hi-Res音质',
    'jyeffect': '高清环绕声',
    'surround': '环绕声',
    'lossless': '无损音质',
    'flac': '无损音质',
    'exhigh': '极高品质',
    '320k': '极高品质',
    'standard': '标准音质',
    '128k': '标准音质',
    '最低': '最低',
    '中等': '中等',
    '较高': '较高',
    '最高': '最高',
  };
  return qualityNames[quality] ?? quality;
}

class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  MorePageState createState() => MorePageState();
}

class MorePageState extends State<MorePage> with WidgetsBindingObserver {
  refresh() {
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    final textColor = brightness == Brightness.dark
        ? CupertinoColors.white
        : CupertinoColors.black;
    final iconColor = brightness == Brightness.dark
        ? CupertinoColors.white
        : CupertinoColors.black;
    final backgroundColor = brightness == Brightness.dark
        ? CupertinoColors.systemGrey6
        : CupertinoColors.systemGroupedBackground;
    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        leading: Padding(
          padding: const EdgeInsets.only(left: 0.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '设置',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 24,
                color: textColor,
              ).useSystemChineseFont(),
            ),
          ),
        ),
      ),
      child: ListView(
        children: [
          CupertinoFormSection.insetGrouped(
            header: Text('应用信息',
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                  prefix: SizedBox(
                      height: 60,
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: imageCacheHelper(""),
                      )),
                  child: Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.only(left: 10),
                        child: Text(
                          'AppRhyme',
                          style: TextStyle(
                            color: textColor,
                            fontSize: 20.0,
                          ).useSystemChineseFont(),
                        ),
                      ))),
              CupertinoFormRow(
                  prefix: Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: Text(
                        '版本号',
                        style:
                            TextStyle(color: textColor).useSystemChineseFont(),
                      )),
                  child: Container(
                      padding: const EdgeInsets.only(right: 10),
                      alignment: Alignment.centerRight,
                      height: 40,
                      child: Text(
                        globalPackageInfo.version,
                        style:
                            TextStyle(color: textColor).useSystemChineseFont(),
                      ))),

              CupertinoFormRow(
                prefix: Text(
                  '项目链接',
                  style: TextStyle(color: textColor).useSystemChineseFont(),
                ),
                child: CupertinoButton(
                  onPressed: openProjectLink,
                  child: Text(
                    'github.com/canxin121/app_rhyme',
                    style: TextStyle(color: textColor).useSystemChineseFont(),
                  ),
                ),
              ),
            ],
          ),
          CupertinoFormSection.insetGrouped(
            header: Text("音频设置",
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                  prefix: Text("清空待播清单",
                      style:
                          TextStyle(color: textColor).useSystemChineseFont()),
                  child: CupertinoButton(
                      child: Icon(
                        CupertinoIcons.clear,
                        color: activeIconRed,
                      ),
                      onPressed: () {
                        globalAudioHandler.clear();
                      }))
            ],
          ),
          _buildJsMusicSourceSection(context, textColor, iconColor),
          _buildQualitySelectSection(context, () {
            setState(() {});
          }, textColor),
          // IOS系统无法直接访问文件系统，且已开启在文件中显示应用数据，所以不显示此选项
          if (!Platform.isIOS)
            _buildExportCacheRoot(context, refresh, textColor, iconColor),
          CupertinoFormSection.insetGrouped(
            header: Text('储存设置',
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                prefix: Padding(
                    padding: const EdgeInsets.only(right: 20),
                    child: Text(
                      '保存歌曲时缓存歌曲封面',
                      style: TextStyle(color: textColor).useSystemChineseFont(),
                    )),
                child: CupertinoSwitch(
                    value: globalConfig.savePicWhenAddMusicList,
                    onChanged: (value) {
                      if (value != globalConfig.savePicWhenAddMusicList) {
                        globalConfig.savePicWhenAddMusicList = value;
                        globalConfig.save();
                        setState(() {});
                      }
                    }),
              ),
              CupertinoFormRow(
                prefix: Padding(
                    padding: const EdgeInsets.only(right: 20),
                    child: Text(
                      '保存歌单时缓存歌曲歌词',
                      style: TextStyle(color: textColor).useSystemChineseFont(),
                    )),
                child: CupertinoSwitch(
                    value: globalConfig.saveLyricWhenAddMusicList,
                    onChanged: (value) {
                      if (value != globalConfig.saveLyricWhenAddMusicList) {
                        globalConfig.saveLyricWhenAddMusicList = value;
                        globalConfig.save();
                        setState(() {});
                      }
                    }),
              ),
              CupertinoFormRow(
                  prefix: Text("清除冗余歌曲数据",
                      style:
                          TextStyle(color: textColor).useSystemChineseFont()),
                  child: CupertinoButton(
                      onPressed: () async {
                        try {
                          await SqlFactoryW.cleanUnusedMusicData();
                          await SqlFactoryW.cleanUnusedMusiclist();
                          refreshMusicListGridViewPage();
                          LogToast.success("储存清理", "清理无用歌曲数据成功",
                              "[MorePage] Cleaned unused music data");
                        } catch (e) {
                          LogToast.error("储存清理", "清理无用歌曲数据失败: $e",
                              "[MorePage] Failed to clean unused music data: $e");
                        }
                      },
                      child: const Icon(
                        CupertinoIcons.bin_xmark,
                        color: CupertinoColors.systemRed,
                      ))),
            ],
          ),
          CupertinoFormSection.insetGrouped(
            header: Text('其他',
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                  prefix: Text("运行日志",
                      style:
                          TextStyle(color: textColor).useSystemChineseFont()),
                  child: CupertinoButton(
                      child: const Icon(
                        CupertinoIcons.book,
                        color: CupertinoColors.activeGreen,
                      ),
                      onPressed: () {
                        Navigator.of(context).push(CupertinoPageRoute(
                          builder: (context) =>
                              TalkerScreen(talker: globalTalker),
                        ));
                      })),
            ],
          ),
        ],
      ),
    );
  }

  CupertinoFormSection _buildJsMusicSourceSection(BuildContext context, Color textColor, Color iconColor) {
    return CupertinoFormSection.insetGrouped(
      header: Text('JS音乐源管理',
          style: TextStyle(color: textColor).useSystemChineseFont()),
      children: [
        CupertinoFormRow(
          prefix: Text(
            'JS音乐源',
            style: TextStyle(color: textColor).useSystemChineseFont(),
          ),
          child: CupertinoButton(
            onPressed: () {
              Navigator.of(context).push(
                CupertinoPageRoute(
                  builder: (context) => const JsMusicSourceManagementPage(),
                ),
              );
            },
            child: Icon(CupertinoIcons.music_note_2, color: iconColor),
          ),
        ),
      ],
    );
  }
}

// ImportExternApiMenu已删除，现在使用JS音乐源管理

CupertinoFormSection _buildQualitySelectSection(
    BuildContext context, void Function() refresh, Color textColor) {
  List<Widget> children = [];

  // 统一为"默认播放音质"选项，不再区分WiFi和移动网络
  children.add(CupertinoFormRow(
      prefix: Text("默认播放音质",
          style: TextStyle(color: textColor).useSystemChineseFont()),
      child: CupertinoButton(
          onPressed: () async {
            String? selectedQuality = await showJsQualitySelectionDialog(context);
            if (selectedQuality != null) {
              // 同时更新WiFi和移动网络音质设置，保持一致性
              globalConfig.wifiAutoQuality = selectedQuality;
              globalConfig.mobileAutoQuality = selectedQuality;
              await globalConfig.save();
            }
            refresh();
          },
          child: Text(_getDisplayQualityName(globalConfig.wifiAutoQuality),
              style: TextStyle(color: textColor).useSystemChineseFont()))));

  return CupertinoFormSection.insetGrouped(
    header:
        Text('音质选择', style: TextStyle(color: textColor).useSystemChineseFont()),
    children: children,
  );
}



CupertinoFormSection _buildExportCacheRoot(BuildContext context,
    void Function() refresh, Color textColor, Color iconColor) {
  Future<void> exportCacheRoot(bool copy) async {
    var path = await pickDirectory();
    if (path == null) return;
    if (globalConfig.exportCacheRoot != null &&
        globalConfig.exportCacheRoot == path) {
      LogToast.info("数据设定", "与原文件夹相同, 无需操作",
          "[exportCacheRoot] Same as original folder, no need to operate");
      return;
    }
    try {
      try {
        if (context.mounted) {
          await showWaitDialog(context, "正在处理中,稍后将自动退出应用以应用更改");
        }
        await globalAudioHandler.clear();
        await SqlFactoryW.shutdown();
        late String originRootPath;
        if (globalConfig.exportCacheRoot != null &&
            globalConfig.exportCacheRoot!.isNotEmpty) {
          originRootPath = globalConfig.exportCacheRoot!;
        } else {
          originRootPath = "$globalDocumentPath/AppRhyme";
        }
        if (copy) {
          await copyDirectory(
              src: "$originRootPath/$picCacheRoot", dst: "$path/$picCacheRoot");
          await copyDirectory(
              src: "$originRootPath/$musicCacheRoot",
              dst: "$path/$musicCacheRoot");
          await copyFile(
              from: "$originRootPath/MusicData.db", to: "$path/MusicData.db");
        }

        globalConfig.lastExportCacheRoot = globalConfig.exportCacheRoot;
        globalConfig.exportCacheRoot = path;
        globalConfig.save();
        if (context.mounted) {
          context.findAncestorStateOfType<MorePageState>()?.refresh();
        }
        await SqlFactoryW.initFromPath(filepath: "$path/MusicData.db");
      } finally {
        if (context.mounted) {
          Navigator.pop(context);
        }
      }
      try {
        if (context.mounted) {
          await showWaitDialog(context,
              "应用将在3秒后退出\n下次打开时将删除旧文件夹下数据, 并应用新文件夹下数据\n如未正常退出, 请关闭应用后重新打开");
        }
        await Future.delayed(const Duration(seconds: 3));
      } finally {
        if (context.mounted) {
          Navigator.pop(context);
        }
        await exitApp();
      }
    } catch (e) {
      LogToast.error("数据设定", "数据设定失败: $e", "[exportCacheRoot] $e");
    }
  }

  List<CupertinoFormRow> children = [];
  if (globalConfig.exportCacheRoot == null) {
    children.add(CupertinoFormRow(
        prefix: Padding(
            padding: const EdgeInsets.only(right: 20),
            child: Text(
              '当前数据状态',
              style: TextStyle(color: textColor).useSystemChineseFont(),
            )),
        child: Container(
            padding: const EdgeInsets.only(right: 10),
            alignment: Alignment.centerRight,
            height: 50,
            child: Text(
              "应用内部数据",
              style: TextStyle(color: textColor).useSystemChineseFont(),
            ))));
  } else {
    children.add(CupertinoFormRow(
        prefix: Padding(
            padding: const EdgeInsets.only(right: 20),
            child: Text(
              '当前数据文件夹',
              style: TextStyle(color: textColor).useSystemChineseFont(),
            )),
        child: Container(
            padding: const EdgeInsets.only(right: 10),
            alignment: Alignment.centerRight,
            height: 50,
            child: Text(
              globalConfig.exportCacheRoot!,
              style: TextStyle(color: textColor).useSystemChineseFont(),
            ))));
  }
  children.add(
    CupertinoFormRow(
      prefix: Text(
        '迁移数据文件夹',
        style: TextStyle(color: textColor).useSystemChineseFont(),
      ),
      child: CupertinoButton(
        onPressed: () async {
          var confirm = await showConfirmationDialog(
              context,
              "注意!\n"
              "迁移数据将会将当前使用文件夹下的数据迁移到新的文件夹下\n"
              "请确保新的文件夹下没有AppRhyme的数据, 否则会导致该文件夹中数据完全丢失!!!\n"
              "如果你想直接使用指定文件夹下的数据, 请使用'使用数据'功能\n"
              "操作后应用将会自动退出, 请重新打开应用以应用更改\n"
              "是否继续?");
          if (confirm != null && confirm) {
            await exportCacheRoot(true);
          }
        },
        child: Icon(CupertinoIcons.folder, color: iconColor),
      ),
    ),
  );
  children.add(
    CupertinoFormRow(
      prefix: Text(
        '使用数据文件夹',
        style: TextStyle(color: textColor).useSystemChineseFont(),
      ),
      child: CupertinoButton(
        onPressed: () async {
          var confirm = await showConfirmationDialog(
              context,
              "注意!\n"
              "使用数据将会直接使用指定文件夹下的数据, 请确保指定下有正确的数据\n"
              "这将会导致当前使用的文件夹下的数据完全丢失!!!\n"
              "如果你想迁移数据, 请使用'迁移数据'功能\n"
              "操作后应用将会自动退出, 请重新打开应用以应用更改\n"
              "是否继续?");
          if (confirm != null && confirm) {
            await exportCacheRoot(false);
          }
        },
        child: Icon(CupertinoIcons.folder, color: iconColor),
      ),
    ),
  );
  return CupertinoFormSection.insetGrouped(
    header:
        Text('数据设定', style: TextStyle(color: textColor).useSystemChineseFont()),
    children: children,
  );
}
