import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

class MusicInfo extends StatefulWidget {
  final double titleHeight;
  final double artistHeight;
  final EdgeInsets padding;
  const MusicInfo(
      {super.key,
      required this.titleHeight,
      required this.artistHeight,
      required this.padding});

  @override
  State<StatefulWidget> createState() => MusicInfoState();
}

class MusicInfoState extends State<MusicInfo> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 音乐名称
          Obx(() {
            final musicName = globalAudioHandler.playingMusic.value?.info.name ?? "Music";
            final artistName = globalAudioHandler.playingMusic.value?.info.artist.join(", ") ?? "Artist";

            return Container(
              alignment: Alignment.topLeft,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 歌曲名称 - 支持长按复制
                  GestureDetector(
                    onLongPress: () {
                      Clipboard.setData(ClipboardData(text: musicName));
                      showCupertinoDialog(
                        context: context,
                        builder: (context) => CupertinoAlertDialog(
                          title: Text('已复制', style: const TextStyle().useSystemChineseFont()),
                          content: Text('歌曲名称已复制到剪贴板', style: const TextStyle().useSystemChineseFont()),
                          actions: [
                            CupertinoDialogAction(
                              child: Text('确定', style: const TextStyle().useSystemChineseFont()),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                          ],
                        ),
                      );
                    },
                    child: Text(
                      musicName,
                      style: TextStyle(
                              fontSize: widget.titleHeight,
                              color: CupertinoColors.systemGrey6)
                          .useSystemChineseFont(),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // 艺术家名称
                  Text(
                    artistName,
                    style: TextStyle(
                            fontSize: widget.artistHeight,
                            color: CupertinoColors.systemGrey5)
                        .useSystemChineseFont(),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
